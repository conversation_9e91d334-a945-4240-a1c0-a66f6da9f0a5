{"app": {"title": "Lost Pet HK", "subtitle": "申請報失動物", "description": "Quickly post missing pet information and let more people help find your pet", "features": {"quickReport": {"title": "Quick Report", "description": "Fill in pet information quickly, complete missing report within 2 minutes"}, "smartSearch": {"title": "Smart Search", "description": "Multi-dimensional filtering to quickly find relevant missing information"}, "socialShare": {"title": "Social Sharing", "description": "Share to major social platforms with one click to expand search range"}}, "recentPosts": {"title": "Recent Lost Pet Posts", "description": "View recently posted missing pet information", "viewAll": "View All", "noPosts": "No posts available", "noPostsDescription": "No missing pet information has been posted yet"}}, "nav": {"home": "Home", "posts": "Posts", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "dashboard": "Dashboard", "myPosts": "My Posts", "createPost": "Post Info", "backToList": "Back to List"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "Login to Your Account", "register": "Register", "registerTitle": "Create New Account", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "phone": "Phone Number", "phoneOptional": "Phone Number (Optional)", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "registerNow": "Register Now", "loginNow": "Login Now", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "logout": "Logout", "agreeTerms": "I agree to the Terms of Service and Privacy Policy", "placeholders": {"username": "Enter username", "email": "Enter email address", "phone": "Enter phone number", "password": "Enter password (at least 6 characters)", "confirmPassword": "Enter password again", "newPassword": "Enter new password (at least 6 characters)", "verificationCode": "Enter 6-digit verification code"}, "forgotPasswordPage": {"title": "Forgot Password", "description": "Please enter your email address and we will send a verification code to your email", "sendCode": "Send Verification Code", "sending": "Sending...", "codeSent": "Verification code has been sent to your email, please check", "continueReset": "Continue to Reset Password", "backToLogin": "Back to Login", "resendCode": "Resend Verification Code"}, "resetPassword": {"title": "Reset Password", "description": "Please enter verification code and new password", "codeSentTo": "Verification code sent to: {email}", "verificationCode": "Verification Code", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resetButton": "Reset Password", "resetting": "Resetting...", "resendCode": "Resend", "countdown": "{seconds}s", "success": "Password reset successfully! Please login with your new password", "backToForgot": "Resend Verification Code", "backToLogin": "Back to Login"}, "validation": {"invalidEmail": "Please enter a valid email address", "checkInput": "Please check your input"}}, "post": {"title": "Posts", "create": "Post Info", "createTitle": "Post Missing Pet Information", "createDescription": "Please fill in pet details to help more people identify your pet", "edit": "Edit Post", "delete": "Delete Post", "view": "View Details", "share": "Share", "searchingFor": "Searching for {name}", "publishedAt": "Published on {date}", "petInfo": "Pet Information", "lostInfo": "Missing Information", "publisher": "Publisher", "relatedVideo": "Related Video", "watchVideo": "Watch Video", "shareToHelp": "Share to Help Find", "shareHelpText": "Your help is very important for finding lost pets, please share with more people", "selectPetMode": "Select Pet Information Method", "selectExistingPet": "Select Existing Pet", "createNewPet": "Create New Pet", "noPetsYet": "No pet information yet", "noPetsHint": "Please add pet information first, or select \"Add New Pet\"", "ageYears": "{age} years old", "shareSighting": "Share Sighting Clue", "steps": {"petInfo": "Pet Basic Information", "lostInfo": "Missing Information", "contactInfo": "Contact Information"}, "status": {"searching": "Searching", "found": "Found", "closed": "Closed"}, "actions": {"publish": "Publish", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "close": "Close Post", "reopen": "Reopen", "nextStep": "Next Step", "publishing": "Publishing...", "publishInfo": "Publish Info"}, "placeholders": {"petName": "Enter pet name", "selectSpecies": "Select species", "selectColor": "Select color", "selectGender": "Select gender", "selectBreed": "e.g., Golden Retriever, Persian Cat, etc.", "petAge": "Enter age", "petDescription": "Describe pet's appearance, such as size, special markings, etc.", "location": "Enter missing location to search", "contactInfo": "If needed, you can provide additional contact information", "videoUrl": "If you have pet videos, please provide the link"}, "labels": {"petBasicInfo": "Pet Basic Information", "appearanceDescription": "Appearance Description (Optional)", "petPhoto": "Pet Photo", "uploadHelpText": "Please upload clear pet photos to help others identify"}, "hints": {"contactInfoHelp": "The system will automatically use your registered email as contact information. You can provide additional contact information here", "unnamedPet": "Unnamed"}, "fields": {"petName": "Pet Name", "petSpecies": "Pet Species", "petGender": "Pet Gender", "petColor": "Main Color", "petAge": "Pet Age", "petBreed": "Specific Breed", "description": "Detailed Description", "location": "Missing Location", "contactInfo": "Contact Information", "contactInfoOptional": "Contact Information (Optional)", "videoUrl": "Video Link (Optional)", "reward": "<PERSON><PERSON> Amount", "images": "Pet Photos", "lostDate": "Missing Date"}, "errors": {"selectPet": "Please select a pet", "createPetFailed": "Failed to create pet information", "createPostFailed": "Failed to create post", "publishFailed": "Publish failed", "publishFailedRetry": "Publish failed, please try again later"}}, "pet": {"species": {"dog": "Dog", "cat": "Cat", "rabbit": "Rabbit", "bird": "<PERSON>", "hamster": "<PERSON><PERSON>", "other": "Other"}, "gender": {"male": "Male", "female": "Female", "unknown": "Unknown"}, "colors": {"black": "Black", "white": "White", "brown": "<PERSON>", "golden": "Golden", "gray": "<PERSON>", "mixed": "Mixed", "other": "Other"}, "actions": {"add": "Add Pet", "edit": "Edit Pet", "delete": "Delete Pet", "details": "Pet Details"}, "modal": {"title": "Pet Details", "operationFailed": "Operation Failed", "unnamed": "Unnamed", "unknown": "Unknown", "ageYears": "{age} years old", "addedAt": "Added At", "deleting": "Deleting...", "deletePet": "Delete Pet", "editInfo": "Edit Info", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete pet \"{name}\"? This action cannot be undone.", "confirmDeleteAction": "Confirm Delete", "deleteFailed": "Delete failed", "deleteFailedRetry": "Delete failed, please try again later"}}, "location": {"districts": {"central": "Central and Western", "wanchai": "<PERSON>", "eastern": "Eastern", "southern": "Southern", "yauTsimMong": "<PERSON><PERSON>", "shamShuiPo": "<PERSON>ham <PERSON>", "kowloonCity": "Kowloon City", "wongTaiSin": "<PERSON>", "kwunTong": "<PERSON><PERSON><PERSON>", "tsuen": "<PERSON><PERSON><PERSON>", "tuenMun": "<PERSON><PERSON>", "yuenLong": "<PERSON><PERSON>", "north": "North", "taiPo": "Tai Po", "shaTin": "<PERSON><PERSON>", "saiKung": "<PERSON>", "kwaiTsing": "<PERSON><PERSON>", "islands": "Islands"}}, "sighting": {"title": "Sightings", "add": "Provide Clue", "view": "View Clues", "description": "Clue Description", "location": "Found Location", "time": "Found Time", "contact": "Contact Information", "submit": "Submit Clue", "modalTitle": "Sighting Clues - {name}", "sightingId": "Sighting Clue #{id}", "count": "clues", "verified": "Verified", "pending": "Pending Verification", "markAsVerified": "<PERSON> as Verified", "cancelVerification": "Cancel Verification", "sightingTime": "Sighting Time", "sightingLocation": "Sighting Location", "sightingDescription": "Description", "submittedAt": "Submitted At", "sightingPhoto": "Sighting Photo", "noSightings": "No clues yet", "noSightingsDescription": "No one has provided sighting clues yet", "operationFailed": "Operation failed: {message}", "operationFailedRetry": "Operation failed, please try again later", "loadSightingsFailed": "Failed to load clues", "submitSuccess": "Submitted Successfully", "submitSuccessText": "Thank you for providing the clue, it will help find the pet", "sightingsAndComments": "Sightings and Comments", "loadingClues": "Loading...", "noSightingsAndComments": "No clues and comments yet", "noSightingsAndCommentsDescription": "No one has provided sighting clues or comments yet", "commentType": "💬 Comment", "clueType": "👁️ Clue", "confirmedStatus": "✓ Verified", "commentImage": "Comment Image", "sightingImage": "Sighting Photo", "submittedTime": "Submitted: {time}", "previousPage": "Previous", "nextPage": "Next", "pageInfo": "Page {current} of {total}", "postComment": "💬 Post Comment", "commentContent": "Comment Content", "commentPlaceholder": "Share your thoughts, suggestions or encouragement...", "anonymousComment": "Anonymous comment, no login required", "submittingComment": "Submitting...", "postCommentAction": "Post Comment", "commentSubmitFailed": "Failed to submit comment, please try again later"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "viewDetails": "View Details", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "noData": "No data", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "previousStep": "Previous Step", "submit": "Submit", "upload": "Upload", "download": "Download", "share": "Share", "copy": "Copy", "close": "Close", "welcome": "Welcome", "listView": "List View", "mapView": "Map View", "filterConditions": "Filter Conditions", "keywordSearch": "Keyword Search", "clearFilters": "Clear Filters", "all": "All", "retry": "Retry", "refresh": "Refresh", "unknown": "Unknown", "noPetInfo": "No pet search information", "adjustFilters": "Try adjusting filter conditions", "noPostsYet": "No one has posted pet search information yet", "clickToView": "Click to view"}, "upload": {"clickToUpload": "Click to Upload", "dragFilesHere": "or drag files here", "supportedFormats": "Supports {formats}, max {size}MB", "uploadImage": "Upload Image", "onlyOneFile": "Only one file can be uploaded", "maxFilesLimit": "Maximum {max} files can be uploaded", "invalidImageFormat": "File {name} is not a valid image format", "fileTooLarge": "File {name} size exceeds {size}MB", "processingFailed": "File processing failed, please try again"}, "message": {"success": {"login": "Login successful", "register": "Registration successful", "logout": "Logout successful", "save": "Save successful", "delete": "Delete successful", "upload": "Upload successful", "submit": "Submit successful"}, "error": {"network": "Network connection failed, please check network settings", "server": "Server error, please try again later", "unauthorized": "<PERSON>gin expired, please login again", "forbidden": "No permission to perform this operation", "notFound": "Requested resource not found", "validation": "Input information is incorrect, please check and try again", "fileTooLarge": "File size exceeds limit", "invalidFileType": "Unsupported file type"}, "confirm": {"delete": "Are you sure you want to delete?", "logout": "Are you sure you want to logout?", "close": "Are you sure you want to close the post?"}}, "form": {"required": "This field is required", "invalid": "Invalid format", "tooShort": "Content too short", "tooLong": "Content too long", "emailInvalid": "Invalid email format", "passwordTooShort": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page {page}", "total": "Total {total} items", "showing": "Showing {start} to {end} of {total} items"}, "search": {"placeholder": "Search pet name, description, etc...", "noResults": "No results found", "resultsCount": "Found {count} results"}, "error": {"loadFailed": "Load failed", "networkError": "Network error", "serverError": "Server error", "unknownError": "Unknown error", "postNotFound": "Post not found", "postNotFoundDescription": "This post may have been deleted or does not exist", "backToList": "Back to List"}, "dashboard": {"description": "Manage your pet information and search posts", "overview": "Overview", "myPosts": "My Posts", "myPets": "My Pets", "profile": "Profile", "welcome": "Welcome back, {name}!", "stats": {"totalPosts": "My Posts", "totalPets": "My Pets", "totalSightings": "Received Clues"}, "quickActions": {"title": "Quick Actions", "publishPost": "Post Missing Info", "publishDescription": "Quickly post your pet's missing information", "browseInfo": "Browse Pet Search Info", "browseDescription": "View other people's pet search information"}, "recentActivity": "Recent Activity", "noPosts": "You haven't posted any pet search information yet", "confirmDeletePost": "Are you sure you want to delete post \"{name}\"? This action cannot be undone.", "myPostsDescription": "Manage your published pet search information", "lostLocation": "Lost location: {location}", "viewSightings": "View Clues", "updateStatusFailed": "Failed to update status: {message}", "updateStatusFailedRetry": "Failed to update status, please try again later", "deleteFailed": "Delete failed: {message}", "deleteFailedRetry": "Delete failed, please try again later", "loadPostsFailed": "Failed to load posts"}, "profile": {"basicInfo": "Basic Information", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "notSet": "Not Set", "registeredAt": "Registration Date", "updateSuccess": "Update successful", "passwordChanged": "Password changed successfully", "accountStats": "Account Statistics", "stats": {"publishedPosts": "Published Posts", "receivedSightings": "Received Clues", "foundPets": "Found Pets"}}}